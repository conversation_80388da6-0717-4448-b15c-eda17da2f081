# 🚀 宝塔面板 Go 项目部署说明

## 📋 部署清单

### 需要上传的文件：
1. **solve-api-linux** - 可执行文件（主程序）
2. **.env.production** - 环境变量配置文件

## ⚙️ 宝塔面板配置要求

### Go SDK 版本：
```
Go 1.19+ （推荐 Go 1.21.x）
```

### 端口配置：
```
应用端口：8080
```

### 防火墙设置：
- 开放 8080 端口（或配置 Nginx 反向代理）

## 📁 部署步骤

### 1. 上传文件
将以下文件上传到服务器目录（如：`/www/wwwroot/solve-api/`）：
- `solve-api-linux`
- `.env.production`

### 2. 重命名配置文件
```bash
mv .env.production .env
```

### 3. 设置可执行权限
```bash
chmod +x solve-api-linux
```

### 4. 在宝塔面板中配置 Go 项目

#### 项目配置：
- **项目名称**：solve-api
- **项目路径**：/www/wwwroot/solve-api/
- **启动文件**：solve-api-linux
- **端口**：8080
- **运行用户**：www

#### 启动命令：
```bash
./solve-api-linux
```

## 🔧 宝塔面板操作

### 在 Go 项目管理中：
1. 点击"添加 Go 项目"
2. 填写项目信息：
   - 项目名称：solve-api
   - 项目路径：/www/wwwroot/solve-api/
   - 启动文件：solve-api-linux
   - 端口：8080
3. 点击"提交"
4. 点击"启动"

### 管理命令：
- **启动**：在宝塔面板点击"启动"
- **停止**：在宝塔面板点击"停止"  
- **重启**：在宝塔面板点击"重启"
- **查看日志**：在宝塔面板查看运行日志

## 🌐 Nginx 反向代理配置（可选）

如果需要通过域名访问，可以配置 Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 验证部署

### 1. 健康检查
```bash
curl http://localhost:8080/health
```
期望返回：
```json
{"status":"ok","message":"服务运行正常"}
```

### 2. 管理员登录测试
```bash
curl -X POST http://localhost:8080/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'
```

### 3. API 测试（需要先创建 API Key）
```bash
curl -X POST http://localhost:8080/api/solve \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"qu_type":"single","img_url":"https://example.com/image.jpg"}'
```

## 🔒 安全建议

1. **修改默认管理员密码**
2. **更换 JWT 密钥**（修改 .env 文件中的 JWT_SECRET）
3. **配置防火墙规则**
4. **启用 HTTPS**（通过宝塔面板配置 SSL）

## 📊 监控和维护

### 查看运行状态：
- 宝塔面板 > Go 项目 > solve-api > 查看状态

### 查看日志：
- 宝塔面板 > Go 项目 > solve-api > 运行日志

### 性能监控：
- 宝塔面板 > 监控 > 查看 CPU、内存使用情况

## 🆘 故障排除

### 常见问题：

1. **启动失败**
   - 检查可执行文件权限：`ls -la solve-api-linux`
   - 检查配置文件：确保 .env 文件存在且格式正确
   - 查看宝塔面板错误日志

2. **端口冲突**
   - 检查 8080 端口是否被占用：`netstat -tuln | grep 8080`
   - 修改 .env 文件中的 SERVER_PORT

3. **数据库连接失败**
   - 检查数据库配置
   - 测试网络连通性：`telnet 47.96.0.212 3380`

4. **API 调用失败**
   - 检查 Qwen API Key 是否有效
   - 查看应用日志

## 📞 技术支持

如遇问题，请检查：
1. Go 版本是否符合要求
2. 文件权限是否正确
3. 配置文件是否完整
4. 网络连接是否正常
