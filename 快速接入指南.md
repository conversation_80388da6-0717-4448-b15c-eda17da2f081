# 🚀 题库解析 API - 快速接入指南

## 📋 基本信息

**服务地址**：`http://************:8080`  
**认证方式**：API Key（Bearer Token）  
**请求格式**：JSON  

## 🔑 第一步：获取 API Key

联系管理员获取您的专属 API Key。

## 📝 第二步：发起请求

### 接口地址
```
POST http://************:8080/api/solve
```

### 请求头
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

### 请求参数
```json
{
  "qu_type": "1",
  "img_url": "https://example.com/question.jpg"
}
```

**参数说明：**
- `qu_type`：业务逻辑类型（当前只支持 `1`，未来可扩展 `2,3,4,5,6...`）
- `img_url`：题目图片的网络地址

## 📊 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "type": "单选题",
    "question": "题目内容",
    "options": {
      "A": "选项A内容",
      "B": "选项B内容",
      "C": "选项C内容",
      "D": "选项D内容"
    },
    "answer": "D",
    "analysis": "详细解析说明"
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 💻 代码示例

### cURL
```bash
curl -X POST http://************:8080/api/solve \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "qu_type": "1",
    "img_url": "https://tiku.uzdns.com/storage/images/20q4976.jpg"
  }'
```

### JavaScript
```javascript
fetch('http://************:8080/api/solve', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    qu_type: '1',
    img_url: 'https://example.com/question.jpg'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### Python
```python
import requests

response = requests.post(
    'http://************:8080/api/solve',
    headers={
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    },
    json={
        'qu_type': '1',
        'img_url': 'https://example.com/question.jpg'
    }
)

result = response.json()
print(result)
```

## ⚠️ 重要提醒

1. **API Key 安全**：请妥善保管您的 API Key，不要在客户端代码中暴露
2. **图片要求**：确保图片 URL 可公网访问，格式为 JPG/PNG/GIF
3. **请求频率**：建议控制并发请求数量，避免超出限制
4. **错误处理**：请根据返回的状态码进行相应的错误处理

## 📞 技术支持

如有问题请联系技术支持团队。

---

**开始使用吧！** 🎉
