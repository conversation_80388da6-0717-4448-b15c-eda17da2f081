# 拍照搜题API v2.0 部署包

## 📦 部署文件清单

### 🚀 可执行文件
```
solve-api-v2.0-final-linux-amd64    11.0MB    x86_64架构 ⭐ 推荐
solve-api-v2.0-final-linux-arm64    11.0MB    ARM64架构
```

### ✅ 最终版本特性
- **MySQL缓存写入完全修复** - JSON序列化问题已解决
- **混合缓存系统完整** - 双写机制和被动恢复完全工作
- **生产环境就绪** - 经过完整测试验证

### ⚙️ 配置文件
```
.env                          开发环境配置
deploy/.env.production        生产环境配置
deploy/env.sh                 环境变量脚本
```

### 🔧 管理脚本
```
deploy/start.sh               启动脚本
deploy/stop.sh                停止脚本
deploy/restart.sh             重启脚本
deploy/status.sh              状态检查脚本
```

### 📚 文档文件
```
deploy/宝塔部署指南.md         详细部署说明
RELEASE_NOTES_v2.0.md         版本发布说明
DEPLOYMENT_PACKAGE.md         本文件
```

## 🎯 快速部署指南

### 1. 确认服务器架构
```bash
uname -m
# x86_64 -> 使用 solve-api-v2.0-final-linux-amd64
# aarch64 -> 使用 solve-api-v2.0-final-linux-arm64
```

### 2. 上传文件到服务器
```bash
# 创建部署目录
mkdir -p /www/wwwroot/solve-api
cd /www/wwwroot/solve-api

# 上传对应架构的可执行文件
# 重命名为 solve-api
mv solve-api-v2.0-linux-amd64 solve-api

# 上传配置和脚本文件
# 上传 deploy/ 目录下的所有文件
```

### 3. 设置权限
```bash
chmod +x solve-api
chmod +x deploy/*.sh
```

### 4. 配置环境变量
```bash
# 编辑生产环境配置
cp deploy/.env.production .env
vi .env

# 或使用环境变量脚本
vi deploy/env.sh
```

### 5. 启动服务
```bash
# 重命名可执行文件
mv solve-api-v2.0-final-linux-amd64 solve-api

# 方法1: 直接启动
./solve-api

# 方法2: 使用脚本启动
./deploy/start.sh
```

### 6. 验证服务
```bash
# 检查服务状态
./deploy/status.sh

# 测试健康检查
curl http://localhost:8080/health
```

## 🔧 环境要求

### 系统要求
- **操作系统**: Linux (CentOS 7+, Ubuntu 18.04+, Debian 9+)
- **架构**: x86_64 或 ARM64
- **内存**: 最低 512MB，推荐 1GB+
- **磁盘**: 最低 100MB 可用空间

### 依赖服务
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 5.0+ (可选，推荐)
- **网络**: 需要访问外部API (Qwen/DeepSeek)

### 端口要求
- **8080**: API服务端口
- **3306**: MySQL数据库端口
- **6379**: Redis缓存端口

## 🛡️ 安全配置

### 必需配置
```bash
# 数据库配置
DB_HOST=your-db-host
DB_PORT=3306
DB_USER=your-db-user
DB_PASSWORD=your-secure-password
DB_NAME=solve

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# API密钥
QWEN_API_KEY=your-qwen-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
```

### 推荐配置
```bash
# 生产模式
GIN_MODE=release

# 安全配置
JWT_SECRET=your-random-jwt-secret
API_RATE_LIMIT=60

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

## 📊 功能特性

### ✨ v2.0 新功能
- 🏗️ **MySQL + Redis 混合缓存** - 双层缓存架构
- 🔄 **智能故障恢复** - 自动降级和恢复
- 📊 **完整缓存统计** - 性能监控和分析
- 🛡️ **生产级安全** - 完整的安全防护
- 🌍 **环境变量支持** - 灵活的配置管理

### 🎯 核心优势
- **数据永不丢失** - MySQL持久化保障
- **高性能缓存** - Redis高速访问
- **自动故障恢复** - 被动恢复机制
- **零运维成本** - 全自动管理
- **生产就绪** - 企业级可靠性

## 🆘 故障排除

### 常见问题
1. **服务无法启动** - 检查端口占用和配置文件
2. **数据库连接失败** - 验证数据库配置和网络连接
3. **Redis连接失败** - 检查Redis服务状态和密码
4. **API调用失败** - 验证API密钥和网络连接

### 日志查看
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep -i error logs/app.log

# 查看服务状态
./deploy/status.sh
```

## 📞 技术支持

### 监控命令
```bash
# 服务状态
./deploy/status.sh

# 进程监控
ps aux | grep solve-api

# 端口监控
netstat -tlnp | grep 8080

# 资源监控
top -p $(cat solve-api.pid)
```

---

**拍照搜题API v2.0 - 企业级混合缓存架构，生产环境就绪！** 🚀
