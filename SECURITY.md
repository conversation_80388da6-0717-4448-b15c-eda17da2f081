# 安全配置指南

## 🔒 环境变量配置





 Docker数据库宝塔信息
外网ipv4面板地址: https://***********:26858/af16fd16
内网面板地址: https://*************:26858/af16fd16
username: ks6qcbae
password: 5f7588d7
### 数据库信息
MySQL 5.6
MySQL 8.0
账号：gmdns
密码：5e7fFn3HpPfuQ6Qx42Az
Name: happy
### 必需的环境变量
在生产环境中，请设置以下环境变量：

```bash
# DeepSeek API Key
export DEEPSEEK_API_KEY="***********************************"

# 数据库配置
export DB_HOST="***********"
export DB_PORT="3306"
export DB_USER="gmdns"
export DB_PASSWORD="5e7fFn3HpPfuQ6Qx42Az"
export DB_NAME="solve"

# Redis配置
export REDIS_HOST="************"
export REDIS_PORT="6379"
export REDIS_PASSWORD="y4HY8xm8dECYmDSeaX8GC"

# Qwen API配置
export QWEN_API_KEY="sk-3920274bedf642c2b7495f534aadca84"
```

## 🛡️ 安全最佳实践

### 1. API Key管理
- 定期轮换API密钥
- 使用强随机生成的密钥
- 设置合理的配额限制
- 启用IP白名单（生产环境）

### 2. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据
- 启用SSL连接

### 3. Redis安全
- 设置强密码
- 绑定特定IP地址
- 禁用危险命令
- 启用SSL/TLS

### 4. 网络安全
- 使用HTTPS（生产环境）
- 配置防火墙规则
- 限制不必要的端口访问

## 🚨 安全检查清单

### 部署前检查
- [ ] 所有敏感信息已移至环境变量
- [ ] 数据库密码已更改
- [ ] Redis密码已更改
- [ ] API密钥已更新
- [ ] CORS配置已限制域名
- [ ] 启用了HTTPS
- [ ] 配置了防火墙规则

### 运行时监控
- [ ] 监控异常API调用
- [ ] 检查错误日志
- [ ] 监控资源使用情况
- [ ] 定期检查访问日志

## 🔧 生产环境配置

### CORS配置
生产环境应该限制CORS域名：
```go
// 替换 main.go 中的 origin = "*" 为具体域名
allowedOrigins := []string{
    "https://yourdomain.com",
    "https://api.yourdomain.com",
}
```

### 日志配置
- 设置适当的日志级别
- 配置日志轮转
- 避免记录敏感信息

### 监控告警
- 设置API调用异常告警
- 监控系统资源使用
- 配置错误率告警

## 📞 安全事件响应

如发现安全问题：
1. 立即撤销相关API密钥
2. 检查访问日志
3. 更新所有密码
4. 通知相关人员
5. 修复安全漏洞
6. 更新安全配置

## 🔄 定期安全维护

### 每月检查
- 更新依赖包
- 检查安全漏洞
- 审查访问日志
- 更新密码策略

### 每季度检查
- 安全渗透测试
- 代码安全审计
- 更新安全文档
- 培训团队成员
