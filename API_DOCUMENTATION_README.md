# 拍照搜题API v2.0 - 文档中心

## 📚 文档导航

### 🚀 快速开始
- **[5分钟快速接入指南](QUICK_START_GUIDE.md)** - 最快速的接入方式
- **[完整API接入指南](API_INTEGRATION_GUIDE.md)** - 详细的接入文档

### 🔧 开发工具
- **[Postman集合文件](Solve_API_Postman_Collection.json)** - 导入Postman直接测试
- **[部署指南](deploy/宝塔部署指南.md)** - 服务器部署说明

### 📋 版本信息
- **[发布说明](RELEASE_NOTES_v2.0.md)** - v2.0版本详细说明
- **[部署包说明](DEPLOYMENT_PACKAGE.md)** - 部署文件清单

## 🎯 API概览

### 核心接口
```
POST /api/solve - 题目解析接口
GET /health     - 健康检查接口
```

### 认证方式
```
Authorization: Bearer your_api_key_here
```

### 支持题型
- **选择题** (qu_type: "1")
- **填空题** (qu_type: "2") 
- **判断题** (qu_type: "3")

## ⚡ 快速测试

### cURL命令
```bash
curl -X POST http://your-domain.com/api/solve \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "qu_type": "1",
    "img_url": "https://example.com/question.jpg"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "type": "选择题",
    "question": "题目内容",
    "options": {
      "A": "选项A",
      "B": "选项B", 
      "C": "选项C",
      "D": "选项D"
    },
    "answer": "A",
    "analysis": "详细解析..."
  }
}
```

## 🏗️ 系统架构

### 缓存系统
- **一级缓存**: Redis (响应时间 ~2秒)
- **二级缓存**: MySQL (响应时间 ~3秒)
- **智能降级**: 自动故障恢复

### 性能指标
- **缓存命中**: 1-3秒响应
- **首次解析**: 10-20秒处理
- **可用性**: 99.9%+

## 🛡️ 安全特性

### 认证授权
- API Key认证
- 请求频率限制
- 安全HTTP头

### 数据保护
- 传输加密
- 访问日志
- 错误监控

## 📞 技术支持

### 获取API密钥
1. 联系系统管理员
2. 或访问管理后台申请

### 问题反馈
- 技术支持：联系管理员
- 服务状态：访问 `/health` 接口
- 错误排查：查看响应状态码

### 开发资源
- **完整文档**: [API_INTEGRATION_GUIDE.md](API_INTEGRATION_GUIDE.md)
- **SDK示例**: 支持JavaScript、Python、PHP、Java
- **测试工具**: Postman集合文件

## 🔄 版本历史

### v2.0 (当前版本)
- ✅ MySQL + Redis混合缓存
- ✅ 智能故障恢复
- ✅ 完整API文档
- ✅ 多语言SDK示例

### v1.0
- ✅ 基础题目识别
- ✅ API Key认证
- ✅ 基础缓存

## 🎉 开始使用

1. **获取API密钥** - 联系管理员
2. **阅读快速指南** - [QUICK_START_GUIDE.md](QUICK_START_GUIDE.md)
3. **导入Postman** - [Solve_API_Postman_Collection.json](Solve_API_Postman_Collection.json)
4. **开始开发** - 参考SDK示例代码

---

**拍照搜题API v2.0 - 让AI为你的应用赋能！** 🚀

### 📁 文档文件列表
```
├── API_INTEGRATION_GUIDE.md      # 完整接入指南
├── QUICK_START_GUIDE.md          # 5分钟快速接入
├── Solve_API_Postman_Collection.json  # Postman测试集合
├── RELEASE_NOTES_v2.0.md         # 版本发布说明
├── DEPLOYMENT_PACKAGE.md         # 部署包说明
└── API_DOCUMENTATION_README.md   # 本文档
```
