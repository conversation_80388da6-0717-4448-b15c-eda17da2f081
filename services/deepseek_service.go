package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"solve-api/models"
)

type DeepSeekRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponse struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

func AnalyzeQuestionWithDeepSeek(questionData string) (string, error) {
	url := "https://api.deepseek.com/v1/chat/completions"

	prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【输出格式】：
答案：[选择正确的选项，如A、B、C、D或Y、N等，要把选项文字带上，多选题要实现答案选项换行]
解析：[要完整的解析，但是不要说来源]`, questionData)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	request := DeepSeekRequest{
		Model:    "deepseek-chat",
		Messages: messages,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+getDeepSeekAPIKey())

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepSeek API错误: %s", string(body))
	}

	var response DeepSeekResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("DeepSeek返回空响应")
	}

	content := response.Choices[0].Message.Content
	fmt.Printf("DeepSeek原始响应: %q\n", content)

	return content, nil
}







// parseDeepSeekAnswer 解析DeepSeek的答案分析
func parseDeepSeekAnswer(content string) (string, string, error) {
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤1 - 原始输入: %q\n", content)

	content = cleanUTF8StringDeepSeek(content)
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤2 - UTF8清理后: %q\n", content)

	content = strings.TrimSpace(content)
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤3 - 去空格后: %q\n", content)

	lines := strings.Split(content, "\n")
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤4 - 分行数量: %d\n", len(lines))

	var answer, analysis string

	for i, line := range lines {
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 处理行: %q\n", i+1, line)

		line = strings.TrimSpace(line)
		if line == "" {
			fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 跳过空行\n", i+1)
			continue
		}

		// 查找冒号分隔符（优先查找半角冒号）
		colonIndex := strings.Index(line, ":")
		if colonIndex == -1 {
			colonIndex = strings.Index(line, "：")
		}
		if colonIndex == -1 {
			fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 未找到冒号，跳过\n", i+1)
			// 如果没有冒号，可能是解析的一部分
			if answer != "" && analysis == "" {
				analysis = line
				fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 设置为分析内容: %q\n", i+1, analysis)
			}
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 解析键值: key=%q, value=%q\n", i+1, key, value)

		value = cleanUTF8StringDeepSeek(value)
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - UTF8清理值后: %q\n", i+1, value)

		switch key {
		case "答案":
			answer = value
			fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 设置答案: %q\n", i+1, answer)
		case "解析":
			analysis = value
			fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤5.%d - 设置解析: %q\n", i+1, analysis)
		}
	}

	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤6 - 初步解析结果: answer=%q, analysis=%q\n", answer, analysis)

	// 如果没有找到标准格式，尝试从整个内容中提取
	if answer == "" {
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤7 - 答案为空，尝试智能提取\n")
		answer = extractAnswerFromContent(content)
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤7 - 智能提取答案: %q\n", answer)
	}

	if analysis == "" {
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤8 - 解析为空，使用整个内容\n")
		analysis = content // 使用整个内容作为解析
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤8 - 设置解析为整个内容: %q\n", analysis)
	}

	// 移除"参考来源"后面的所有内容
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤9 - 移除参考来源前: %q\n", analysis)
	analysis = removeReferenceSource(analysis)
	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤9 - 移除参考来源后: %q\n", analysis)

	if answer == "" {
		fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤10 - 最终答案为空，返回错误\n")
		return "", "", fmt.Errorf("未找到答案")
	}

	fmt.Printf("🔍 [parseDeepSeekAnswer] 步骤10 - 最终结果: answer=%q, analysis=%q\n", answer, analysis)
	return answer, analysis, nil
}



// removeReferenceSource 移除"参考来源"以及相关参考信息后面的所有内容
func removeReferenceSource(analysis string) string {
	fmt.Printf("🔍 [removeReferenceSource] 输入: %q\n", analysis)

	// 查找"参考来源"的位置
	index := strings.Index(analysis, "参考来源")
	if index != -1 {
		result := strings.TrimSpace(analysis[:index])
		fmt.Printf("🔍 [removeReferenceSource] 找到'参考来源'，截取后: %q\n", result)
		return result
	}

	// 查找"此答案参考"的位置
	index = strings.Index(analysis, "此答案参考")
	if index != -1 {
		result := strings.TrimSpace(analysis[:index])
		fmt.Printf("🔍 [removeReferenceSource] 找到'此答案参考'，截取后: %q\n", result)
		return result
	}

	// 查找"参考自"的位置
	index = strings.Index(analysis, "参考自")
	if index != -1 {
		result := strings.TrimSpace(analysis[:index])
		fmt.Printf("🔍 [removeReferenceSource] 找到'参考自'，截取后: %q\n", result)
		return result
	}

	// 如果没有找到任何参考关键词，返回原内容
	fmt.Printf("🔍 [removeReferenceSource] 未找到参考关键词，返回原内容: %q\n", analysis)
	return analysis
}

// ParseQwenExtraction 解析Qwen提取的题目信息（导出函数）
func ParseQwenExtraction(content string) (*models.QuestionData, error) {
	return parseQwenExtraction(content)
}

// ParseDeepSeekResponse 解析DeepSeek的答案分析（导出函数）
func ParseDeepSeekResponse(content string) (string, string, error) {
	return parseDeepSeekAnswer(content)
}

// parseQwenExtraction 解析Qwen提取的题目信息
func parseQwenExtraction(content string) (*models.QuestionData, error) {
	// 首先标准化内容
	content = normalizeQwenContent(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符（现在查找半角冒号，因为已经标准化为半角）
		colonIndex := strings.Index(line, ":")
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		value = strings.Trim(value, "[]")

		// 标准化key，移除多余空格
		key = strings.ReplaceAll(key, " ", "")

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		case "图片项":
			// 图片描述信息，可以存储但不影响核心逻辑
			if value != "无" && value != "" {
				questionData.ImageDescription = value
			}
		// 支持判断题的Y/N选项格式
		case "选项Y":
			questionData.Options["Y"] = value
		case "选项N":
			questionData.Options["N"] = value
		// 支持其他可能的选项格式
		case "选项1":
			questionData.Options["1"] = value
		case "选项2":
			questionData.Options["2"] = value
		case "选项3":
			questionData.Options["3"] = value
		case "选项4":
			questionData.Options["4"] = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// normalizeQwenContent 标准化Qwen返回的内容
func normalizeQwenContent(content string) string {
	// 1. 清理UTF-8字符串
	content = cleanUTF8StringDeepSeek(content)
	content = strings.TrimSpace(content)

	// 2. 统一所有标点符号为半角（避免编码问题）
	replacements := map[string]string{
		// 全角转半角
		"，": ",",
		"。": ".",
		"；": ";",
		"：": ":",
		"？": "?",
		"！": "!",
		"（": "(",
		"）": ")",
		"【": "[",
		"】": "]",
	}

	for fullWidth, halfWidth := range replacements {
		content = strings.ReplaceAll(content, fullWidth, halfWidth)
	}

	// 3. 统一引号为半角
	content = strings.ReplaceAll(content, "\u201c", "\"")  // 全角双引号 → 半角双引号
	content = strings.ReplaceAll(content, "\u201d", "\"")  // 全角双引号 → 半角双引号
	content = strings.ReplaceAll(content, "\u2018", "'")   // 全角单引号 → 半角单引号
	content = strings.ReplaceAll(content, "\u2019", "'")   // 半角单引号 → 半角单引号

	// 4. 移除多余的空格，但保留换行符用于解析
	lines := strings.Split(content, "\n")
	var normalizedLines []string
	for _, line := range lines {
		// 移除行内多余空格
		line = strings.Join(strings.Fields(line), " ")
		line = strings.TrimSpace(line)
		if line != "" {
			normalizedLines = append(normalizedLines, line)
		}
	}

	return strings.Join(normalizedLines, "\n")
}

// extractAnswerFromContent 从内容中智能提取答案
func extractAnswerFromContent(content string) string {
	fmt.Printf("🔍 [extractAnswerFromContent] 开始智能提取答案，输入: %q\n", content)

	// 查找"选项X"模式的答案
	var foundAnswers []string

	// 分析每一行，查找明确的选项引用
	lines := strings.Split(content, "\n")
	fmt.Printf("🔍 [extractAnswerFromContent] 分析行数: %d\n", len(lines))

	for i, line := range lines {
		fmt.Printf("🔍 [extractAnswerFromContent] 分析第%d行: %q\n", i+1, line)

		if strings.Contains(line, "选项") {
			fmt.Printf("🔍 [extractAnswerFromContent] 第%d行包含'选项'\n", i+1)
			// 提取选项字母
			for _, char := range "ABCD" {
				if strings.Contains(line, "选项"+string(char)) ||
				   strings.Contains(line, "选项 "+string(char)) {
					fmt.Printf("🔍 [extractAnswerFromContent] 第%d行找到选项%s\n", i+1, string(char))
					// 检查这一行是否表示这是正确答案
					if !strings.Contains(line, "不正确") &&
					   !strings.Contains(line, "错误") &&
					   !strings.Contains(line, "不成立") &&
					   !strings.Contains(line, "不符合") {
						found := false
						for _, existing := range foundAnswers {
							if existing == string(char) {
								found = true
								break
							}
						}
						if !found {
							foundAnswers = append(foundAnswers, string(char))
							fmt.Printf("🔍 [extractAnswerFromContent] 添加答案选项: %s\n", string(char))
						}
					} else {
						fmt.Printf("🔍 [extractAnswerFromContent] 第%d行选项%s被排除（包含否定词）\n", i+1, string(char))
					}
				}
			}
		}
	}

	fmt.Printf("🔍 [extractAnswerFromContent] 第一轮提取结果: %v\n", foundAnswers)

	// 如果没有找到明确的选项引用，使用简单的字母检测
	if len(foundAnswers) == 0 {
		fmt.Printf("🔍 [extractAnswerFromContent] 第一轮未找到，开始简单字母检测\n")
		for _, char := range "ABCD" {
			if strings.Contains(content, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
				fmt.Printf("🔍 [extractAnswerFromContent] 简单检测找到: %s\n", string(char))
			}
		}

		// 对于判断题，优先查找明确的答案指示
		if strings.Contains(content, "答案是Y") || strings.Contains(content, "答案为Y") ||
		   strings.Contains(content, "选择Y") || strings.Contains(content, "正确答案是Y") {
			foundAnswers = append(foundAnswers, "Y")
			fmt.Printf("🔍 [extractAnswerFromContent] 判断题检测找到: Y\n")
		} else if strings.Contains(content, "答案是N") || strings.Contains(content, "答案为N") ||
		          strings.Contains(content, "选择N") || strings.Contains(content, "正确答案是N") {
			foundAnswers = append(foundAnswers, "N")
			fmt.Printf("🔍 [extractAnswerFromContent] 判断题检测找到: N\n")
		} else if strings.Contains(content, "答案为\"错误\"") || strings.Contains(content, "答案是\"错误\"") ||
		          strings.Contains(content, "题目描述的行为是错误的") {
			foundAnswers = append(foundAnswers, "N")
			fmt.Printf("🔍 [extractAnswerFromContent] 判断题检测找到: N (错误)\n")
		} else if strings.Contains(content, "答案为\"正确\"") || strings.Contains(content, "答案是\"正确\"") ||
		          strings.Contains(content, "题目描述的行为是正确的") {
			foundAnswers = append(foundAnswers, "Y")
			fmt.Printf("🔍 [extractAnswerFromContent] 判断题检测找到: Y (正确)\n")
		}
	}

	// 返回找到的答案
	result := ""
	if len(foundAnswers) > 0 {
		result = strings.Join(foundAnswers, "")
	}

	fmt.Printf("🔍 [extractAnswerFromContent] 最终提取结果: %q\n", result)
	return result
}







// cleanUTF8StringDeepSeek 清理UTF-8字符串
func cleanUTF8StringDeepSeek(s string) string {
	// 只移除BOM字符，不移除其他字符，避免误删正常内容
	s = strings.TrimPrefix(s, "\uFEFF")
	return s
}

// getDeepSeekAPIKey 从环境变量获取DeepSeek API Key
func getDeepSeekAPIKey() string {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		// 如果环境变量未设置，使用默认值（仅用于开发环境）
		apiKey = "sk-dd3347aa018244b1a2e19bb364c3c97e"
	}
	return apiKey
}