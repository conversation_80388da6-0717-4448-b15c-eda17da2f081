package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"solve-api/models"
)

type DeepSeekRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponse struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

func AnalyzeQuestionWithDeepSeek(questionData string) (string, error) {
	url := "https://api.deepseek.com/v1/chat/completions"

	prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【输出格式】：
答案：[选择正确的选项，如A、B、C、D或Y、N等，要把选项文字带上，多选题要实现答案选项换行]
解析：[要完整的解析，但是不要说来源]`, questionData)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	request := DeepSeekRequest{
		Model:    "deepseek-chat",
		Messages: messages,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+getDeepSeekAPIKey())

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepSeek API错误: %s", string(body))
	}

	var response DeepSeekResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("DeepSeek返回空响应")
	}

	content := response.Choices[0].Message.Content
	fmt.Printf("DeepSeek原始响应: %q\n", content)

	return content, nil
}







// parseDeepSeekAnswer 解析DeepSeek的答案分析
func parseDeepSeekAnswer(content string) (string, string, error) {
	content = cleanUTF8StringDeepSeek(content)
	content = strings.TrimSpace(content)
	lines := strings.Split(content, "\n")

	var answer, analysis string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符（优先查找半角冒号）
		colonIndex := strings.Index(line, ":")
		if colonIndex == -1 {
			colonIndex = strings.Index(line, "：")
		}
		if colonIndex == -1 {
			// 如果没有冒号，可能是解析的一部分
			if answer != "" && analysis == "" {
				analysis = line
			}
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		value = cleanUTF8StringDeepSeek(value)

		switch key {
		case "答案":
			answer = value
		case "解析":
			analysis = value
		}
	}

	// 如果没有找到标准格式，尝试从整个内容中提取
	if answer == "" {
		answer = extractAnswerFromContent(content)
	}

	if analysis == "" {
		analysis = content // 使用整个内容作为解析
	}

	// 移除"参考来源"后面的所有内容
	analysis = removeReferenceSource(analysis)

	if answer == "" {
		return "", "", fmt.Errorf("未找到答案")
	}

	return answer, analysis, nil
}



// removeReferenceSource 移除"参考来源"以及相关参考信息后面的所有内容
func removeReferenceSource(analysis string) string {
	// 查找"参考来源"的位置
	index := strings.Index(analysis, "参考来源")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"此答案参考"的位置
	index = strings.Index(analysis, "此答案参考")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"参考自"的位置
	index = strings.Index(analysis, "参考自")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 如果没有找到任何参考关键词，返回原内容
	return analysis
}

// ParseQwenExtraction 解析Qwen提取的题目信息（导出函数）
func ParseQwenExtraction(content string) (*models.QuestionData, error) {
	return parseQwenExtraction(content)
}

// ParseDeepSeekResponse 解析DeepSeek的答案分析（导出函数）
func ParseDeepSeekResponse(content string) (string, string, error) {
	return parseDeepSeekAnswer(content)
}

// parseQwenExtraction 解析Qwen提取的题目信息
func parseQwenExtraction(content string) (*models.QuestionData, error) {
	// 首先标准化内容
	content = normalizeQwenContent(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符（现在查找半角冒号，因为已经标准化为半角）
		colonIndex := strings.Index(line, ":")
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		value = strings.Trim(value, "[]")

		// 标准化key，移除多余空格
		key = strings.ReplaceAll(key, " ", "")

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		case "图片项":
			// 图片描述信息，可以存储但不影响核心逻辑
			if value != "无" && value != "" {
				questionData.ImageDescription = value
			}
		// 支持判断题的Y/N选项格式
		case "选项Y":
			questionData.Options["Y"] = value
		case "选项N":
			questionData.Options["N"] = value
		// 支持其他可能的选项格式
		case "选项1":
			questionData.Options["1"] = value
		case "选项2":
			questionData.Options["2"] = value
		case "选项3":
			questionData.Options["3"] = value
		case "选项4":
			questionData.Options["4"] = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// normalizeQwenContent 标准化Qwen返回的内容
func normalizeQwenContent(content string) string {
	// 1. 清理UTF-8字符串
	content = cleanUTF8StringDeepSeek(content)
	content = strings.TrimSpace(content)

	// 2. 统一所有标点符号为半角（避免编码问题）
	replacements := map[string]string{
		// 全角转半角
		"，": ",",
		"。": ".",
		"；": ";",
		"：": ":",
		"？": "?",
		"！": "!",
		"（": "(",
		"）": ")",
		"【": "[",
		"】": "]",
	}

	for fullWidth, halfWidth := range replacements {
		content = strings.ReplaceAll(content, fullWidth, halfWidth)
	}

	// 3. 统一引号为半角
	content = strings.ReplaceAll(content, "\u201c", "\"")  // 全角双引号 → 半角双引号
	content = strings.ReplaceAll(content, "\u201d", "\"")  // 全角双引号 → 半角双引号
	content = strings.ReplaceAll(content, "\u2018", "'")   // 全角单引号 → 半角单引号
	content = strings.ReplaceAll(content, "\u2019", "'")   // 半角单引号 → 半角单引号

	// 4. 移除多余的空格，但保留换行符用于解析
	lines := strings.Split(content, "\n")
	var normalizedLines []string
	for _, line := range lines {
		// 移除行内多余空格
		line = strings.Join(strings.Fields(line), " ")
		line = strings.TrimSpace(line)
		if line != "" {
			normalizedLines = append(normalizedLines, line)
		}
	}

	return strings.Join(normalizedLines, "\n")
}

// extractAnswerFromContent 从内容中智能提取答案
func extractAnswerFromContent(content string) string {
	// 查找"选项X"模式的答案
	var foundAnswers []string

	// 分析每一行，查找明确的选项引用
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		if strings.Contains(line, "选项") {
			// 提取选项字母
			for _, char := range "ABCD" {
				if strings.Contains(line, "选项"+string(char)) ||
				   strings.Contains(line, "选项 "+string(char)) {
					// 检查这一行是否表示这是正确答案
					if !strings.Contains(line, "不正确") &&
					   !strings.Contains(line, "错误") &&
					   !strings.Contains(line, "不成立") &&
					   !strings.Contains(line, "不符合") {
						found := false
						for _, existing := range foundAnswers {
							if existing == string(char) {
								found = true
								break
							}
						}
						if !found {
							foundAnswers = append(foundAnswers, string(char))
						}
					}
				}
			}
		}
	}

	// 如果没有找到明确的选项引用，使用简单的字母检测
	if len(foundAnswers) == 0 {
		for _, char := range "ABCD" {
			if strings.Contains(content, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}

		// 对于判断题，优先查找明确的答案指示
		if strings.Contains(content, "答案是Y") || strings.Contains(content, "答案为Y") ||
		   strings.Contains(content, "选择Y") || strings.Contains(content, "正确答案是Y") {
			foundAnswers = append(foundAnswers, "Y")
		} else if strings.Contains(content, "答案是N") || strings.Contains(content, "答案为N") ||
		          strings.Contains(content, "选择N") || strings.Contains(content, "正确答案是N") {
			foundAnswers = append(foundAnswers, "N")
		} else if strings.Contains(content, "答案为\"错误\"") || strings.Contains(content, "答案是\"错误\"") ||
		          strings.Contains(content, "题目描述的行为是错误的") {
			foundAnswers = append(foundAnswers, "N")
		} else if strings.Contains(content, "答案为\"正确\"") || strings.Contains(content, "答案是\"正确\"") ||
		          strings.Contains(content, "题目描述的行为是正确的") {
			foundAnswers = append(foundAnswers, "Y")
		}
	}

	// 返回找到的答案
	if len(foundAnswers) > 0 {
		return strings.Join(foundAnswers, "")
	}

	return ""
}







// cleanUTF8StringDeepSeek 清理UTF-8字符串
func cleanUTF8StringDeepSeek(s string) string {
	// 只移除BOM字符，不移除其他字符，避免误删正常内容
	s = strings.TrimPrefix(s, "\uFEFF")
	return s
}

// getDeepSeekAPIKey 从环境变量获取DeepSeek API Key
func getDeepSeekAPIKey() string {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		// 如果环境变量未设置，使用默认值（仅用于开发环境）
		apiKey = "sk-dd3347aa018244b1a2e19bb364c3c97e"
	}
	return apiKey
}