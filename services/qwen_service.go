package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"solve-api/config"
	"solve-api/models"
	"strings"
	"unicode/utf8"
)

// QwenRequest Qwen-VL API 请求结构体
type QwenRequest struct {
	Model string `json:"model"`
	Input struct {
		Messages []QwenMessage `json:"messages"`
	} `json:"input"`
	Parameters struct {
		ResultFormat string `json:"result_format"`
	} `json:"parameters"`
}

type QwenMessage struct {
	Role    string        `json:"role"`
	Content []QwenContent `json:"content"`
}

type QwenContent struct {
	Type  string `json:"type"`
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenResponse Qwen-VL API 响应结构体
type QwenResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content []struct {
					Text string `json:"text"`
				} `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// QwenService Qwen-VL 服务
type QwenService struct {
	apiKey string
	apiURL string
}

// NewQwenService 创建 Qwen-VL 服务实例
func NewQwenService() *QwenService {
	return &QwenService{
		apiKey: config.AppConfig.QwenAPI.APIKey,
		apiURL: config.AppConfig.QwenAPI.APIURL,
	}
}

// AnalyzeImage 分析图片并返回结构化数据
func (s *QwenService) AnalyzeImage(imageURL string) (*models.QuestionData, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := `这是一张驾校考试的题目图片。请专注识别题目相关内容，忽略所有界面元素。

【重要说明】：
- 图片中可能包含"下一题"、"上一题"等按钮，请完全忽略这些界面元素
- 只关注题目文字、选项文字和题干相关的图片内容
- 题干图片通常位于选项下方，是题目的一部分，需要描述

【任务要求】：
1. 仔细识别图片中的题目类型（单选题/多选题/判断题）
2. 完整准确地提取题目问题内容
3. 准确识别所有选项内容
4. 只识别题目内容，不要分析答案
5. 如果题干包含图片内容，需要详细描述图片中与题目相关的信息

【识别要求】：
- 必须准确识别每一个题目相关的文字
- 必须完整提取题目内容和所有选项
- 忽略所有按钮、导航栏等界面元素
- 不要遗漏任何题目相关信息
- 不要添加自己的理解或分析

【输出格式】（严格按照此格式）：
题目类型：[单选题/多选题/判断题]
问题：[完整的题目内容，必须与图片中完全一致]
选项A：[选项A的完整内容]
选项B：[选项B的完整内容]
选项C：[选项C的完整内容]
选项D：[选项D的完整内容]
图片项：[如果题干包含图片，详细描述图片内容；如果没有图片则输出"无"]

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 每行一个字段，格式必须严格一致
- 只识别题目内容，忽略所有界面元素
- 确保题目内容完整准确`

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return nil, fmt.Errorf("未获取到有效响应")
	}

	// 解析结构化文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	questionData, err := parseStructuredText(rawContent)
	if err != nil {
		return nil, fmt.Errorf("解析题目数据失败: %v, 原始内容: %s", err, rawContent)
	}

	return questionData, nil
}

// cleanUTF8String 彻底清理字符串，确保是有效的 UTF-8
func cleanUTF8String(s string) string {
	// 移除 BOM
	s = strings.TrimPrefix(s, "\uFEFF")

	// 移除替换字符
	s = strings.ReplaceAll(s, "\ufffd", "")

	// 确保是有效的 UTF-8
	if !utf8.ValidString(s) {
		s = strings.ToValidUTF8(s, "")
	}

	return s
}

// parseStructuredText 解析结构化文本格式
func parseStructuredText(content string) (*models.QuestionData, error) {
	// 彻底清理内容
	content = cleanUTF8String(content)
	content = strings.TrimSpace(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符
		colonIndex := strings.Index(line, "：")
		if colonIndex == -1 {
			// 尝试英文冒号
			colonIndex = strings.Index(line, ":")
		}
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])

		// 移除可能的方括号
		value = strings.Trim(value, "[]")

		// 彻底清理值
		value = cleanUTF8String(value)

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		case "图片项":
			// 图片描述信息，可以存储但不影响核心逻辑
			if value != "无" && value != "" {
				questionData.ImageDescription = value
			}
		case "答案":
			questionData.Answer = value
		case "解析":
			questionData.Analysis = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if questionData.Answer == "" {
		return nil, fmt.Errorf("缺少答案")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// AnalyzeImageRaw 分析图片并返回原始文本响应（用于传递给DeepSeek）
func (s *QwenService) AnalyzeImageRaw(imageURL string) (string, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := `这是一张驾校考试的题目图片。请专注识别题目相关内容，忽略所有界面元素。

【重要说明】：
- 图片中可能包含"下一题"、"上一题"等按钮，请完全忽略这些界面元素
- 只关注题目文字、选项文字和题干相关的图片内容
- 题干图片通常位于选项下方，是题目的一部分，需要描述

【任务要求】：
1. 仔细识别图片中的题目类型（单选题/多选题/判断题）
2. 完整准确地提取题目问题内容
3. 准确识别所有选项内容
4. 只识别题目内容，不要分析答案
5. 如果题干包含图片内容，需要详细描述图片中与题目相关的信息

【识别要求】：
- 必须准确识别每一个题目相关的文字
- 必须完整提取题目内容和所有选项
- 忽略所有按钮、导航栏等界面元素
- 不要遗漏任何题目相关信息
- 不要添加自己的理解或分析

【输出格式】（严格按照此格式）：
题目类型：[单选题/多选题/判断题]
问题：[完整的题目内容，必须与图片中完全一致]
选项A：[选项A的完整内容]
选项B：[选项B的完整内容]
选项C：[选项C的完整内容]
选项D：[选项D的完整内容]
图片项：[如果题干包含图片，详细描述图片内容；如果没有图片则输出"无"]

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 每行一个字段，格式必须严格一致
- 只识别题目内容，忽略所有界面元素
- 确保题目内容完整准确`

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return "", fmt.Errorf("未获取到有效响应")
	}

	// 返回原始文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	return rawContent, nil
}
