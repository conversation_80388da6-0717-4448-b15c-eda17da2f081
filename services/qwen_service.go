package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"solve-api/config"
	"solve-api/models"
	"strings"
	"unicode/utf8"
)

// QwenRequest Qwen-VL API 请求结构体
type QwenRequest struct {
	Model string `json:"model"`
	Input struct {
		Messages []QwenMessage `json:"messages"`
	} `json:"input"`
	Parameters struct {
		ResultFormat string `json:"result_format"`
	} `json:"parameters"`
}

type QwenMessage struct {
	Role    string        `json:"role"`
	Content []QwenContent `json:"content"`
}

type QwenContent struct {
	Type  string `json:"type"`
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenResponse Qwen-VL API 响应结构体
type QwenResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content []struct {
					Text string `json:"text"`
				} `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// QwenService Qwen-VL 服务
type QwenService struct {
	apiKey string
	apiURL string
}

// NewQwenService 创建 Qwen-VL 服务实例
func NewQwenService() *QwenService {
	return &QwenService{
		apiKey: config.AppConfig.QwenAPI.APIKey,
		apiURL: config.AppConfig.QwenAPI.APIURL,
	}
}

// AnalyzeImage 分析图片并返回结构化数据
func (s *QwenService) AnalyzeImage(imageURL string) (*models.QuestionData, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := `你是非常专业的视觉分析AI，我非常相信与认可你。

【重要说明】：
- 图片中可能存在题干图片，用专业知识分析与题干内容有关的图片，不要去分析任何界面按钮元素；

【任务要求】：
1. 必须识别题目类型
2. 必须完整准确的识别题目内容
3. 必须完整的识别所有选项内容
4. 把自己当成专业的高精度的ocr就好，不要去分析答案

【识别要求】：
- 必须高精度识别所有文字
- 如果有与题干关联的图中图，需要根据题目内容作出图片的详细分析。

【必须严格按照下方格式输出识别到的内容】
类型；
题目；
选项A的完整内容；
选项B的完整内容；
选项C的完整内容；
选项D的完整内容；
图中图的解释，如果没有图中图则无需处理此行；

【特别注意】：
确认好换行！
确认好文字识别！`

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return nil, fmt.Errorf("未获取到有效响应")
	}

	// 解析结构化文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text

	// 在终端清晰显示Qwen原始返回数据
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🔍 [Qwen-VL] 原始返回数据:")
	fmt.Println(strings.Repeat("-", 80))
	fmt.Println(rawContent)
	fmt.Println(strings.Repeat("-", 80))
	fmt.Printf("📊 [Token使用量] 输入: %d, 输出: %d, 总计: %d\n",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)
	fmt.Println(strings.Repeat("=", 80) + "\n")

	// 同时记录到日志
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	questionData, err := parseStructuredText(rawContent)
	if err != nil {
		return nil, fmt.Errorf("解析题目数据失败: %v, 原始内容: %s", err, rawContent)
	}

	return questionData, nil
}

// cleanUTF8String 彻底清理字符串，确保是有效的 UTF-8
func cleanUTF8String(s string) string {
	// 移除 BOM
	s = strings.TrimPrefix(s, "\uFEFF")

	// 移除替换字符
	s = strings.ReplaceAll(s, "\ufffd", "")

	// 确保是有效的 UTF-8
	if !utf8.ValidString(s) {
		s = strings.ToValidUTF8(s, "")
	}

	return s
}

// normalizeQwenResponse 格式化Qwen返回的原始数据
func normalizeQwenResponse(content string) string {
	// 1. 清理UTF-8字符串
	content = cleanUTF8String(content)
	content = strings.TrimSpace(content)

	// 2. 统一标点符号为半角
	punctuationMap := map[string]string{
		"：": ":",  // 全角冒号 → 半角冒号
		"，": ",",  // 全角逗号 → 半角逗号
		"。": ".",  // 全角句号 → 半角句号
		"；": ";",  // 全角分号 → 半角分号
		"？": "?",  // 全角问号 → 半角问号
		"！": "!",  // 全角感叹号 → 半角感叹号
		"（": "(",  // 全角左括号 → 半角左括号
		"）": ")",  // 全角右括号 → 半角右括号
		"【": "[",  // 全角左方括号 → 半角左方括号
		"】": "]",  // 全角右方括号 → 半角右方括号
		"\u201c": `"`, // 全角左双引号 → 半角双引号
		"\u201d": `"`, // 全角右双引号 → 半角双引号
		"\u2018": `'`, // 全角左单引号 → 半角单引号
		"\u2019": `'`, // 全角右单引号 → 半角单引号
	}

	for fullWidth, halfWidth := range punctuationMap {
		content = strings.ReplaceAll(content, fullWidth, halfWidth)
	}

	// 3. 处理行内容
	lines := strings.Split(content, "\n")
	var normalizedLines []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue // 跳过空行
		}

		// 4. 统一冒号格式：确保冒号后只有一个空格
		if strings.Contains(line, ":") {
			// 分割键值对
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				line = key + ": " + value // 统一格式：key: value
			}
		}

		// 5. 清理多余空格（保留单个空格）
		line = strings.Join(strings.Fields(line), " ")

		normalizedLines = append(normalizedLines, line)
	}

	return strings.Join(normalizedLines, "\n")
}

// parseStructuredText 解析结构化文本格式
func parseStructuredText(content string) (*models.QuestionData, error) {
	// 首先格式化原始数据
	content = normalizeQwenResponse(content)
	fmt.Printf("🔧 [格式化后] Qwen数据: %q\n", content)

	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fmt.Printf("🔧 [解析第%d行] 内容: %q\n", i+1, line)

		// 查找冒号分隔符（支持中文和英文冒号）
		colonIndex := strings.Index(line, "：")
		if colonIndex == -1 {
			colonIndex = strings.Index(line, ":")
		}
		if colonIndex == -1 {
			fmt.Printf("🔧 [解析第%d行] 跳过：未找到冒号\n", i+1)
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])

		// 移除可能的方括号和引号
		value = strings.Trim(value, "[]\"")

		// 彻底清理值
		value = cleanUTF8String(value)

		fmt.Printf("🔧 [解析第%d行] 键值对: key=%q, value=%q\n", i+1, key, value)

		switch key {
		case "type", "题目类型":
			questionData.Type = value
			fmt.Printf("🔧 [解析第%d行] 设置题目类型: %q\n", i+1, value)
		case "title", "titel", "question", "问题":
			questionData.Question = value
			fmt.Printf("🔧 [解析第%d行] 设置题目内容: %q\n", i+1, value)
		case "options_a", "option_a", "选项A":
			questionData.Options["A"] = value
			fmt.Printf("🔧 [解析第%d行] 设置选项A: %q\n", i+1, value)
		case "options_b", "option_b", "选项B":
			questionData.Options["B"] = value
			fmt.Printf("🔧 [解析第%d行] 设置选项B: %q\n", i+1, value)
		case "options_c", "option_c", "选项C":
			questionData.Options["C"] = value
			fmt.Printf("🔧 [解析第%d行] 设置选项C: %q\n", i+1, value)
		case "options_d", "option_d", "选项D":
			questionData.Options["D"] = value
			fmt.Printf("🔧 [解析第%d行] 设置选项D: %q\n", i+1, value)
		case "img", "image", "图片项":
			// 图片描述信息，只有在有实际内容时才存储
			if value != "" && value != "无" && value != "none" && value != "null" {
				questionData.ImageDescription = value
				fmt.Printf("🔧 [解析第%d行] 设置图片描述: %q\n", i+1, value)
			} else {
				fmt.Printf("🔧 [解析第%d行] 跳过图片描述（无内容或为'无'）\n", i+1)
			}
		case "答案", "answer":
			questionData.Answer = value
			fmt.Printf("🔧 [解析第%d行] 设置答案: %q\n", i+1, value)
		case "解析", "analysis":
			questionData.Analysis = value
			fmt.Printf("🔧 [解析第%d行] 设置解析: %q\n", i+1, value)
		default:
			fmt.Printf("🔧 [解析第%d行] 未识别的键名: %q\n", i+1, key)
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if questionData.Answer == "" {
		return nil, fmt.Errorf("缺少答案")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// AnalyzeImageRaw 分析图片并返回原始文本响应（用于传递给DeepSeek）
func (s *QwenService) AnalyzeImageRaw(imageURL string) (string, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := `你是非常专业的视觉分析AI，我非常相信与认可你。

【重要说明】：
- 图片中可能存在题干图片，用专业知识分析与题干内容有关的图片，不要去分析任何界面按钮元素；

【任务要求】：
1. 必须识别题目类型
2. 必须完整准确的识别题目内容
3. 必须完整的识别所有选项内容
4. 把自己当成专业的高精度的ocr就好，不要去分析答案

【识别要求】：
- 必须高精度识别所有文字
- 如果有与题干关联的图中图，需要根据题目内容作出图片的详细分析。

【必须严格按照下方格式输出识别到的内容】
类型；
题目；
选项A的完整内容；
选项B的完整内容；
选项C的完整内容；
选项D的完整内容；
图中图的解释，如果没有图中图则无需处理此行；

【特别注意】：
确认好换行！
确认好文字识别！`

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return "", fmt.Errorf("未获取到有效响应")
	}

	// 返回原始文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text

	// 在终端清晰显示Qwen原始返回数据
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🔍 [Qwen-VL Raw] 原始返回数据:")
	fmt.Println(strings.Repeat("-", 80))
	fmt.Println(rawContent)
	fmt.Println(strings.Repeat("-", 80))
	fmt.Printf("📊 [Token使用量] 输入: %d, 输出: %d, 总计: %d\n",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)
	fmt.Println(strings.Repeat("=", 80) + "\n")

	// 同时记录到日志
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	return rawContent, nil
}
