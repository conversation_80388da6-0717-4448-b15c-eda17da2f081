# 拍照搜题API - 5分钟快速接入指南

## 🚀 三步接入

### 1️⃣ 获取API密钥
联系管理员获取API密钥，格式如：`ak_xxxxxxxxxxxxxxxxx`

### 2️⃣ 发送请求
```bash
curl -X POST http://your-domain.com/api/solve \
  -H "Authorization: Bearer your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "qu_type": "1",
    "img_url": "https://example.com/question.jpg"
  }'
```

### 3️⃣ 获取结果
```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "type": "选择题",
    "question": "题目内容",
    "options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C",
      "D": "选项D"
    },
    "answer": "A",
    "analysis": "详细解析..."
  }
}
```

## 📋 参数说明

| 参数 | 值 | 说明 |
|------|---|------|
| qu_type | "1" | 选择题 |
| qu_type | "2" | 填空题 |
| qu_type | "3" | 判断题 |
| img_url | 图片URL | 支持JPG/PNG/GIF |

## ⚡ 快速测试

### JavaScript
```javascript
fetch('http://your-domain.com/api/solve', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    qu_type: '1',
    img_url: 'https://example.com/test.jpg'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### Python
```python
import requests

response = requests.post('http://your-domain.com/api/solve', 
  headers={
    'Authorization': 'Bearer your_api_key',
    'Content-Type': 'application/json'
  },
  json={
    'qu_type': '1',
    'img_url': 'https://example.com/test.jpg'
  }
)
print(response.json())
```

### PHP
```php
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://your-domain.com/api/solve');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
  'Authorization: Bearer your_api_key',
  'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
  'qu_type' => '1',
  'img_url' => 'https://example.com/test.jpg'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
echo $response;
```

## 🔍 常见问题

**Q: 401错误怎么办？**
A: 检查API密钥是否正确

**Q: 图片识别不准确？**
A: 确保图片清晰完整

**Q: 响应很慢？**
A: 首次解析需要10-20秒，后续相同图片会缓存

## 📞 获取帮助

- 详细文档：查看 `API_INTEGRATION_GUIDE.md`
- 技术支持：联系系统管理员
- 服务状态：访问 `http://your-domain.com/health`

---

**开始使用拍照搜题API，让AI为你的应用赋能！** 🎯
