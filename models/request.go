package models

// SolveRequest 解题请求结构体
type SolveRequest struct {
	QuType string `json:"qu_type" binding:"required" example:"1"`
	ImgURL string `json:"img_url" binding:"required" example:"https://example.com/image.jpg"`
}

// SolveResponse 解题响应结构体
type SolveResponse struct {
	Code    int         `json:"code" example:"200"`
	Message string      `json:"message" example:"success"`
	Data    interface{} `json:"data,omitempty"`
}

// QuestionData Qwen-VL 返回的题目数据结构
type QuestionData struct {
	Type     string            `json:"type" example:"单选题"`
	Question string            `json:"question" example:"题目内容"`
	Options  map[string]string `json:"options"`
	Answer   string            `json:"answer" example:"A"`
	Analysis string            `json:"analysis" example:"解析内容"`
}

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Code    int    `json:"code" example:"400"`
	Message string `json:"message" example:"参数错误"`
	Error   string `json:"error,omitempty"`
}

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"admin"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Code    int    `json:"code" example:"200"`
	Message string `json:"message" example:"登录成功"`
	Data    struct {
		Token    string `json:"token"`
		Username string `json:"username"`
		Role     string `json:"role"`
	} `json:"data"`
}

// CreateAPIKeyRequest 创建 API Key 请求
type CreateAPIKeyRequest struct {
	QuotaLimit  int    `json:"quota_limit" example:"1000"`
	IPWhitelist string `json:"ip_whitelist" example:""`
}

// CreateAPIKeyResponse 创建 API Key 响应
type CreateAPIKeyResponse struct {
	Code    int    `json:"code" example:"200"`
	Message string `json:"message" example:"创建成功"`
	Data    struct {
		ID          uint   `json:"id"`
		Key         string `json:"key"`
		QuotaLimit  int    `json:"quota_limit"`
		IPWhitelist string `json:"ip_whitelist"`
	} `json:"data"`
}

// APIKeyListResponse API Key 列表响应
type APIKeyListResponse struct {
	Code    int    `json:"code" example:"200"`
	Message string `json:"message" example:"获取成功"`
	Data    struct {
		Total int         `json:"total"`
		Items interface{} `json:"items"` // 使用 interface{} 避免循环导入
	} `json:"data"`
}

// APICallLogListResponse API 调用日志列表响应
type APICallLogListResponse struct {
	Code    int    `json:"code" example:"200"`
	Message string `json:"message" example:"获取成功"`
	Data    struct {
		Total int         `json:"total"`
		Items interface{} `json:"items"` // 使用 interface{} 避免循环导入
	} `json:"data"`
}
