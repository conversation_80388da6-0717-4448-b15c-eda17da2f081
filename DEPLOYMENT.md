# 宝塔面板部署指南

## 📋 部署前准备

### 1. 服务器要求
- Linux 系统（CentOS 7+/Ubuntu 18+）
- 已安装宝塔面板
- Go 1.19+ 环境
- 至少 1GB 内存
- 至少 1GB 磁盘空间

### 2. 宝塔面板配置
- 开放端口：8080（API 服务端口）
- 安装 Nginx（用于反向代理）
- 安装 PM2 管理器（可选，用于进程管理）

## 🚀 部署步骤

### 步骤 1: 上传项目文件
1. 将整个项目文件夹上传到服务器（如：`/www/wwwroot/solve-api/`）
2. 确保所有文件权限正确

### 步骤 2: 安装 Go 环境
```bash
# 如果服务器没有 Go 环境，在宝塔终端执行：
wget https://golang.org/dl/go1.21.0.linux-amd64.tar.gz
tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

### 步骤 3: 设置脚本权限
```bash
cd /www/wwwroot/solve-api/
chmod +x *.sh
```

### 步骤 4: 构建项目
```bash
./build.sh
```

### 步骤 5: 配置生产环境
1. 编辑 `.env.production` 文件，确保数据库配置正确
2. 确保 Qwen API Key 有效

### 步骤 6: 启动服务
```bash
./start.sh
```

### 步骤 7: 配置 Nginx 反向代理
在宝塔面板中添加站点，配置 Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        access_log off;
    }
}
```

## 🔧 管理命令

### 启动服务
```bash
./start.sh
```

### 停止服务
```bash
./stop.sh
```

### 重启服务
```bash
./restart.sh
```

### 查看状态
```bash
./status.sh
```

### 查看日志
```bash
tail -f /tmp/solve-api.log
```

## 🔒 安全配置

### 1. 防火墙设置
- 在宝塔面板安全设置中开放 8080 端口
- 或者只允许内网访问，通过 Nginx 代理

### 2. SSL 证书
- 在宝塔面板中为域名配置 SSL 证书
- 强制 HTTPS 访问

### 3. 访问限制
- 配置 IP 白名单
- 设置访问频率限制

## 📊 监控和维护

### 1. 日志监控
```bash
# 实时查看日志
tail -f /tmp/solve-api.log

# 查看错误日志
grep "ERROR" /tmp/solve-api.log

# 查看访问统计
grep "POST /api/solve" /tmp/solve-api.log | wc -l
```

### 2. 性能监控
```bash
# 查看进程资源使用
./status.sh

# 查看系统资源
top -p $(cat /tmp/solve-api.pid)
```

### 3. 定期维护
- 定期清理日志文件
- 监控数据库连接状态
- 检查 API Key 使用情况

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用：`netstat -tuln | grep 8080`
   - 查看日志：`cat /tmp/solve-api.log`
   - 检查配置文件：`.env` 文件是否正确

2. **数据库连接失败**
   - 检查数据库配置
   - 测试网络连通性：`telnet *********** 3380`
   - 检查防火墙设置

3. **API 调用失败**
   - 检查 Qwen API Key 是否有效
   - 查看网络连接
   - 检查请求格式

### 紧急恢复
```bash
# 强制停止
pkill -f solve-api

# 清理 PID 文件
rm -f /tmp/solve-api.pid

# 重新启动
./start.sh
```

## 📞 技术支持

如遇到部署问题，请检查：
1. 服务器系统兼容性
2. Go 环境版本
3. 网络连接状态
4. 配置文件正确性
