Qwen的prompt



【重要说明】：
- 图片中可能包含"下一题"、"上一题"等按钮，请完全忽略这些界面元素
- 只关注题目文字、选项文字和题干相关的图片内容
- 题干图片通常位于选项下方，是题目的一部分，需要描述

【任务要求】：
1. 仔细识别图片中的题目类型（单选题/多选题/判断题）
2. 完整准确地提取题目问题内容
3. 准确识别所有选项内容
4. 只识别题目内容，不要分析答案
5. 如果题干包含图片内容，需要详细描述图片中与题目相关的信息

【识别要求】：
- 必须准确识别每一个题目相关的文字
- 必须完整提取题目内容和所有选项
- 忽略所有按钮、导航栏等界面元素
- 不要遗漏任何题目相关信息
- 不要添加自己的理解或分析

【输出格式】（严格按照此格式）：
题目类型：[单选题/多选题/判断题]
问题：[完整的题目内容，必须与图片中完全一致]
选项A：[选项A的完整内容]
选项B：[选项B的完整内容]
选项C：[选项C的完整内容]
选项D：[选项D的完整内容]
图片项：[如果题干包含图片，详细描述图片内容；如果没有图片则输出"无"]

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 每行一个字段，格式必须严格一致
- 只识别题目内容，忽略所有界面元素
- 确保题目内容完整准确

https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg"
https://www.bonuspoints.uzdns.com/20250604132329967855981.jpg"
https://www.bonuspoints.uzdns.com/20250604132436c60ed6660.jpg"