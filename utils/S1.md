Qwen的prompt

【重要说明】：
- 请完全忽略界面元素比如按钮、导航栏等
- 只关注题目文字、选项文字和题干相关的图片内容
- 题干图片通常位于选项下方，是题目的一部分，需要描述

【任务要求】：
1. 需要精准且完整的识别题目类型、题目内容、选项内容
2. 如果题干包含图片，需要详细描述图片中与题目相关的信息
3. 只识别题目内容，不要分析答案

【识别要求】：
- 必须准确识别每一个题目相关的文字
- 必须完整提取题目内容和所有选项
- 忽略所有按钮、导航栏等界面元素

【输出格式】（严格按照此格式）：
type:单选题/多选题/判断题
titel:题目内容
options_a:选项A的完整内容
options_b:选项B的完整内容
options_c:选项C的完整内容
options_d:选项D的完整内容
img:题干图片的详细分析，如果没有图片则不输出此行

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 每行一个字段，格式必须严格一致
- 只识别题目内容，忽略所有界面元素
- 确保题目内容完整准确

https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg"
https://www.bonuspoints.uzdns.com/20250604132329967855981.jpg"
https://www.bonuspoints.uzdns.com/20250604132436c60ed6660.jpg"
