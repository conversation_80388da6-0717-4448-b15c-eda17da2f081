Qwen的prompt

这是一张驾校考试的题目图片，题干内容可能存在图片，题干图片的位置位于题干选项的下方。有可能会识别到一个蓝色按钮写着下一题。这是下一题按钮并不是题干图片。

【任务要求】：
1. 仔细识别图片中的题目类型（单选题/多选题/判断题）
2. 完整准确地提取题目问题内容
3. 准确识别所有选项内容
4. 关于题干，你只需要做到精准识别，不需要分析问题答案。
5. 如果题干存在图片你需要将图片内容细节完整的详细的描述出来。

【识别要求】：
- 必须准确识别每一个文字
- 必须完整提取题目内容
- 必须准确识别所有选项
- 不要遗漏任何信息
- 不要添加自己的理解或分析

【输出格式】（严格按照此格式，输出题目内容）：
题目类型：[单选题/多选题/判断题]
问题：[完整的题目内容，必须与图片中完全一致]
选项A：[选项A的完整内容]
选项B：[选项B的完整内容]
选项C：[选项C的完整内容]
选项D：[选项D的完整内容]
图片项：[完整的描述图片的细节,如果题干不存在图片则此项内容输出无]

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 每行一个字段，格式必须严格一致
- 只负责文字识别，不要分析答案
- 确保题目内容完整准确



prompt := fmt.Sprintf(`你是一个专业的驾校考试题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 如果图片项的描述里
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项
4. 如果是多选题，每个正确选项单独一行

【输出格式】：
答案：[具体格式如下]
- 单选题：A 或 B 或 C 或 D
- 多选题：A\nC 或 B\nD（每个选项一行）
- 判断题：Y 或 N

解析：[完整的解析说明，不要包含参考来源信息]`, questionData)


1. 拿到qwen返回的数据后，先进行空格过滤，与标点符转义。建议使用正则处理，将所有标点符号转换为英文符号，将所有空格过滤掉。但是保留换行符。

2. 判断问题类型，然后决定如何执行逻辑，问题类型分为单选题，多选题，判断题。

3. 判断题
    3.1 判断题只有2个选项。Y：正确与N：错误；

    进行结构化解析
    

{"qu_type":"1","img_url":"https:\/\/www.bonuspoints.uzdns.com\/20250604132258f988d2353.jpg"}多选题样本
{"qu_type":"1","img_url":"https:\/\/www.bonuspoints.uzdns.com\/20250604132329967855981.jpg"}判断题样本
{"qu_type":"1","img_url":"https:\/\/www.bonuspoints.uzdns.com\/20250604132436c60ed6660.jpg"}单选题样本