# API Key 鉴权系统开发文档

## 一、项目目标

构建一个高性能、高安全性、可控的 API Key 鉴权系统，支持：
- 第三方系统通过 API Key 接口访问服务
- Web 后台界面用于管理 API Key（创建、禁用、限额配置）
- 支持速率限制、用量计费、调用日志记录
- 可扩展多种认证策略（IP 白名单、签名机制、时间窗限流等）

## 二、系统模块划分

### 1. API 网关服务（Gin 实现）
- 接收并校验带有 API Key 的请求
- 通过中间件验证权限和额度
- 返回标准化 JSON 结构结果

### 2. API Key 管理后台
- 技术栈：Vue（前端） + Gin（REST 接口）
- 实现功能：创建 Key、禁用启用、设置限额、查看调用记录
- 期望是AK、SK来高效防止爆破

### 3. 数据库设计（MySQL）

#### 表：api_keys
| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 主键 |
| key | varchar(128) | 唯一 API Key（如 ak_xxx）|
| user_id | int | 绑定的用户ID（可选）|
| status | enum('active','revoked') | 状态 |
| quota_limit | int | 每月最大调用次数 |
| quota_used | int | 当前已用调用次数 |
| ip_whitelist | text | 白名单IP列表（可选）|
| created_at | datetime | 创建时间 |
| last_used_at | datetime | 最近调用时间 |

#### 表：api_call_logs
| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 主键 |
| api_key_id | int | 外键关联 api_keys.id |
| path | varchar(255) | 请求路径 |
| method | varchar(10) | 请求方法 |
| ip | varchar(45) | 请求 IP |
| timestamp | datetime | 调用时间 |
| status_code | int | 返回状态码 |
| response_time | float | 响应时长（毫秒） |

...

## 九、核心功能接口设计说明

### 1. 接口路径
```
POST /api/solve
```

### 2. 请求 Header
```
Authorization: Bearer {API_KEY}
Content-Type: application/json
```

### 3. 请求参数（JSON）
| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| qu_type | string | ✅ 必填 | 题目类型（如 single、multi、judge）|
| img_url | string | ✅ 必填 | 图片的公网 URL，系统将基于此进行识别 |

### 4. 业务处理
1. 根据qu_type来执行不同的分支流程（目前仅一个分支）
2. 校验请求参数合法性（qu_type正确/img_url可访问）
3. 将img_url发送给Qwen-vl解析。（要求Qwen-vl严格按照json返回）
4. 校验Qwen-vl返回的json，合格后将json返回给请求者。
5. 考虑各种异常情况下的code返回的约定
//需要约束Qwen的json格式我前面给你讲过。



### 5. 安全与健壮性注意点
- ✅ 参数校验：确保 qu_type 合法、img_url 可访问
- ✅ 请求频控：防止单用户暴力并发提交（可按 key 控制）
- ✅ 日志记录：记录 img_url、识别结果、IP、响应时间
- ✅ 业务错误处理：



【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
4. 必须给出明确的答案选项

【输出格式】：
答案：[选择正确的选项，如A、B、C、D或Y、N等，要把选项文字带上，多选题要实现答案选项换行]
解析：[要完整的解析，但是不要说来源]